import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';
import '../widgets/agent_chat_app_bar.dart';
import '../widgets/chat_input_bar.dart';
import '../widgets/message_list.dart';
import '../widgets/choice_buttons.dart';
import '../widgets/choice_loading_indicator.dart';

class AgentChatView extends StatelessWidget {
  const AgentChatView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatPlayerBloc, ChatPlayerState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: [
              _buildAgentBackground(state.participants.isNotEmpty ? state.participants.first.imageUrl : null),
              Column(
                children: [
                  AgentChatAppBar(state: state),
                  Expanded(
                    child: MessageList(
                      messages: state.messages,
                      participants: state.participants,
                      isStoryMode: state.storyDetail != null,
                      protagonistAgent: state.protagonistAgent,
                      customPadding: const EdgeInsets.fromLTRB(16, 100, 16, 8),
                    ),
                  ),
                  // 新增：选项生成加载指示器
                  if (state.isGeneratingChoices && (state.currentChoices == null || state.currentChoices!.isEmpty))
                    const ChoiceLoadingIndicator(),
                  // 新增：当有选项时，显示选项按钮
                  if (state.currentChoices != null && state.currentChoices!.isNotEmpty)
                    const ChoiceButtons(),
                  // 只在回复文字时显示输入指示器（不与选项生成冲突）
                  if (state.isReplying && !state.isGeneratingChoices)
                    _buildTypingIndicator(context),
                  ChatInputBar(state: state),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建动态模糊背景（带平滑切换动画）
  Widget _buildAgentBackground(String? imageUrl) {
    return Positioned.fill(
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 800), // 淡入淡出动画时长
        child: Container(
          key: ValueKey<String?>(imageUrl), // 关键: 以URL为Key触发动画
          decoration: BoxDecoration(
            image: imageUrl != null && imageUrl.isNotEmpty
                ? DecorationImage(
                    image: NetworkImage(imageUrl),
                    fit: BoxFit.cover,
                    alignment: Alignment.topCenter,
                  )
                : null,
            gradient: imageUrl == null || imageUrl.isEmpty
                ? const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFF2A1B3D), Color(0xFF44318D)],
                  )
                : null,
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15.0, sigmaY: 15.0), // 增强模糊效果
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.4),
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypingIndicator(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          const SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(strokeWidth: 2, color: AppColors.secondaryText),
          ),
          const SizedBox(width: 8),
          Text(
            '对方正在输入...',
            style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14),
          ),
        ],
      ),
    );
  }


}
