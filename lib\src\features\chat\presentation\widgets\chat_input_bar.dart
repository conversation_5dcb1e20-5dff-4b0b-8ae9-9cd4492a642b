import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';

class ChatInputBar extends StatefulWidget {
  final ChatPlayerState state;
  const ChatInputBar({super.key, required this.state});

  @override
  State<ChatInputBar> createState() => _ChatInputBarState();
}

class _ChatInputBarState extends State<ChatInputBar> {
  final _textController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (_textController.text.trim().isNotEmpty) {
      // 优先使用当前目标Agent，如果没有则使用第一个参与者
      final targetAgentId = widget.state.currentTargetAgent?.id ?? 
          (widget.state.participants.isNotEmpty ? widget.state.participants.first.id : null);
      
      context.read<ChatPlayerBloc>().add(
        SendMessage(
          content: _textController.text.trim(),
          targetAgentId: targetAgentId,
        )
      );
      _textController.clear();
      FocusScope.of(context).unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatState = widget.state;
    final bool isReplying = chatState.isReplying;
    final bool canInput = !isReplying;

    return SafeArea(
      top: false,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 1. 快捷操作按钮行（分离式设计）
            _buildQuickActions(),
            const SizedBox(height: 12),

            // 2. 输入区域（分离式设计）
            Row(
              children: [
                // 左侧功能按钮
                _buildGlassButton(
                  icon: Icons.add,
                  onTap: () => _showMoreOptions(context),
                ),
                const SizedBox(width: 12),

                // 中间输入框
                Expanded(child: _buildInputField(canInput, isReplying)),
                const SizedBox(width: 12),

                // 右侧发送按钮
                _buildSendButton(canInput, isReplying),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 构建快捷操作按钮（竞品样式）
  Widget _buildQuickActions() {
    final actions = [
      {'icon': Icons.touch_app, 'label': '摸头', 'command': '[摸头]'},
      {'icon': Icons.favorite, 'label': '抱抱', 'command': '[抱抱]'},
      {'icon': Icons.auto_awesome, 'label': '夸夸', 'command': '[夸夸]'},
      {'icon': Icons.cake, 'label': '投喂', 'command': '[投喂]'},
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: actions.map((action) => _buildActionChip(action)).toList(),
    );
  }

  Widget _buildActionChip(Map<String, dynamic> action) {
    return GestureDetector(
      onTap: () {
        context.read<ChatPlayerBloc>().add(
          SendMessage(content: action['command'] as String),
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(action['icon'] as IconData, color: Colors.white, size: 16),
                const SizedBox(width: 6),
                Text(action['label'] as String, style: const TextStyle(color: Colors.white, fontSize: 12)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建毛玻璃按钮
  Widget _buildGlassButton({required IconData icon, required VoidCallback onTap}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Icon(icon, color: Colors.white, size: 24),
          ),
        ),
      ),
    );
  }

  // 构建输入框
  Widget _buildInputField(bool canInput, bool isReplying) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: TextField(
            controller: _textController,
            enabled: canInput,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: isReplying ? 'TA正在输入...' : '说点什么...',
              hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
            ),
            onSubmitted: canInput ? (_) => _sendMessage() : null,
          ),
        ),
      ),
    );
  }

  // 构建发送按钮
  Widget _buildSendButton(bool canInput, bool isReplying) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: GestureDetector(
          onTap: canInput ? _sendMessage : null,
          child: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              gradient: canInput
                  ? const LinearGradient(colors: [AppColors.accentPurple, AppColors.accentBlue])
                  : null,
              color: canInput ? null : Colors.white.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Icon(
              isReplying ? Icons.hourglass_empty : Icons.send,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  // 显示更多选项
  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: AppColors.secondaryBg,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('更多功能', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildMoreOption(Icons.photo, '相册'),
                _buildMoreOption(Icons.camera_alt, '拍照'),
                _buildMoreOption(Icons.mic, '语音'),
                _buildMoreOption(Icons.location_on, '位置'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoreOption(IconData icon, String label) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppColors.accentPurple.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: AppColors.accentPurple, size: 30),
        ),
        const SizedBox(height: 8),
        Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }


}
