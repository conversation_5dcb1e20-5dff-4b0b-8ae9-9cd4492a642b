import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';
import 'package:xinglian/src/common/widgets/glassmorphism_container.dart';

class ChatInputBar extends StatefulWidget {
  final ChatPlayerState state;
  const ChatInputBar({super.key, required this.state});

  @override
  State<ChatInputBar> createState() => _ChatInputBarState();
}

class _ChatInputBarState extends State<ChatInputBar> {
  final _textController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (_textController.text.trim().isNotEmpty) {
      // 优先使用当前目标Agent，如果没有则使用第一个参与者
      final targetAgentId = widget.state.currentTargetAgent?.id ?? 
          (widget.state.participants.isNotEmpty ? widget.state.participants.first.id : null);
      
      context.read<ChatPlayerBloc>().add(
        SendMessage(
          content: _textController.text.trim(),
          targetAgentId: targetAgentId,
        )
      );
      _textController.clear();
      FocusScope.of(context).unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    // 从 Bloc 状态中获取当前的交互状态
    final chatState = widget.state;
    final bool isReplying = chatState.isReplying;
    final bool canInput = !isReplying;

    String hintText = '输入消息...';
    if (isReplying) hintText = '对方正在输入...';

    return SafeArea(
      top: false,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 功能按钮行
            _buildFunctionButtons(),
            const SizedBox(height: 8),
            // 输入框行
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _textController,
                    enabled: canInput, // 根据状态控制是否可编辑
                    style: TextStyle(color: canInput ? AppColors.primaryText : AppColors.secondaryText),
                    decoration: InputDecoration(
                      hintText: hintText,
                      hintStyle: const TextStyle(color: AppColors.secondaryText),
                      filled: true,
                      fillColor: canInput ? AppColors.secondaryBg : AppColors.secondaryBg.withOpacity(0.5),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30.0),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onSubmitted: canInput ? (_) => _sendMessage() : null,
                  ),
                ),
                const SizedBox(width: 8),
            IconButton(
              icon: Icon(
                isReplying ? Icons.hourglass_empty : Icons.send,
                color: canInput ? AppColors.accentPurple : AppColors.tertiaryText,
              ),
              onPressed: canInput ? _sendMessage : null, // 根据状态控制是否可点击
            ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快捷互动按钮行（使用玻璃拟态效果）
  Widget _buildFunctionButtons() {
    final functions = [
      {
        'icon': Icons.touch_app_outlined,
        'label': '摸摸头',
        'command': '[触摸]',
        'color': AppColors.accentPurple,
      },
      {
        'icon': Icons.favorite_border,
        'label': '要抱抱',
        'command': '[拥抱]',
        'color': AppColors.accentPurple,
      },
      {
        'icon': Icons.monitor_heart_outlined,
        'label': '心跳瞬间',
        'command': '[心跳]',
        'color': AppColors.accentPurple,
      },
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: functions.map((function) {
        return GestureDetector(
          onTap: () {
            // 发送指令性文本
            context.read<ChatPlayerBloc>().add(
              SendMessage(content: function['command'] as String)
            );
          },
          child: GlassmorphismStyles.button(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    function['icon'] as IconData,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    function['label'] as String,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }


}
