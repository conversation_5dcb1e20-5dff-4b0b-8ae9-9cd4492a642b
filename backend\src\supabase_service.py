#!/usr/bin/env python3
"""
Supabase服务层 - V5.0 重构版
处理所有数据库操作，基于统一的消息流模型。
"""

import os
import asyncio
import traceback
from typing import List, Dict, Any, Optional
import httpx
from supabase import create_client, Client, ClientOptions
from postgrest.exceptions import APIError
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# --- 通用重试装饰器 ---
def retry_on_db_error(retries=3, delay=1, backoff=2):
    """
    一个装饰器，用于在数据库操作失败时重试。
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if "EOF occurred in violation of protocol" in str(e) or "connection closed" in str(e).lower():
                        print(f"WARN: 在 {func.__name__} 中检测到数据库连接错误 (尝试 {attempt + 1}/{retries})。正在重试...")
                        await asyncio.sleep(delay * (backoff ** attempt))
                    else:
                        print(f"ERROR: 在 {func.__name__} 中发生非重试类型的数据库错误: {e}")
                        raise e
            print(f"ERROR: 在 {func.__name__} 中，所有重试均失败。最后一次错误: {last_exception}")
            raise last_exception
        return wrapper
    return decorator

class SupabaseService:
    """封装所有Supabase数据库操作"""
    
    def __init__(self):
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("ERROR: 缺少Supabase配置，请检查.env文件中的SUPABASE_URL和SUPABASE_SERVICE_ROLE_KEY")
        
        # 最终修复方案: 根据 supabase-py 源码，直接注入一个自定义配置的 httpx 客户端
        # 这是解决 "EOF occurred" 和 "TypeError" 的根本方法。
        # 增加超时时间以解决网络超时问题
        httpx_client = httpx.Client(
            http2=False,
            timeout=httpx.Timeout(
                connect=30.0,  # 连接超时：30秒（默认5秒）
                read=60.0,     # 读取超时：60秒（默认5秒）
                write=30.0,    # 写入超时：30秒（默认5秒）
                pool=120.0     # 连接池超时：120秒（默认5秒）
            ),
            limits=httpx.Limits(
                max_connections=100,    # 最大连接数
                max_keepalive_connections=20  # 最大保持连接数
            )
        )
        opts = ClientOptions(
            httpx_client=httpx_client,
            postgrest_client_timeout=60.0,  # PostgREST客户端超时：60秒
            storage_client_timeout=60.0,    # 存储客户端超时：60秒
            function_client_timeout=60.0    # 函数客户端超时：60秒
        )

        self.supabase: Client = create_client(supabase_url, supabase_key, options=opts)
        print("INFO: Supabase客户端初始化成功 (已通过自定义httpx客户端禁用HTTP/2，并优化超时设置)")

    # ========================================
    # 用户档案相关操作
    # ========================================
    
    async def get_user_profile_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """根据用户ID获取用户档案"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("user_profiles").select("*").eq("user_id", user_id).maybe_single().execute()
            )
            if response:
                return response.data
            return None
        except Exception as e:
            print(f"ERROR: 获取用户档案失败: {e}")
            traceback.print_exc()
            return None

    # ========================================
    # 智能体 (模板) 相关操作
    # ========================================
    
    async def get_agents(self, user_id: Optional[str] = None, is_public: Optional[bool] = None, limit: int = 50, include_system: bool = False) -> List[Dict[str, Any]]:
        """获取智能体列表，并包含创作者信息"""
        try:
            # --- ▼▼▼ 修改开始 ▼▼▼ ---
            # 直接调用已经存在的、能正确获取创作者信息的RPC函数
            # 注意：这个RPC函数自带了is_public=true的筛选和排序，所以下面的代码需要调整
            if is_public:
                response = await asyncio.to_thread(
                    lambda: self.supabase.rpc('get_public_agents_with_creator', {'p_limit': limit}).execute()
                )
                return response.data or []

            # 如果不是只查询public的，则回退到原来的查询逻辑，但去掉错误的关联查询
            else:
                query = self.supabase.table("agents").select("*") # 去掉了 ", user_profiles(display_name)"
                if not include_system:
                    query = query.eq("is_system_agent", False)
                if user_id:
                    query = query.eq("user_id", user_id)
                query = query.order("created_at", desc=True).limit(limit)
                response = await asyncio.to_thread(lambda: query.execute())
                return response.data or []
            # --- ▲▲▲ 修改结束 ▲▲▲ ---
        except Exception as e:
            print(f"ERROR: 获取智能体列表失败: {e}")
            raise e

    async def get_agent_by_id(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取智能体详情"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agents").select("*").eq("id", agent_id).single().execute()
            )
            if response.data:
                return response.data
            return None
        except Exception as e:
            print(f"ERROR: 获取智能体详情失败: {e}")
            return None

    async def create_agent(self, user_id: str, **kwargs) -> Optional[Dict[str, Any]]:
        """创建新智能体"""
        try:
            agent_data = {"user_id": user_id, **kwargs}
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agents").insert(agent_data, returning="representation").execute()
            )
            if response.data:
                print(f"SUCCESS: 智能体 '{kwargs.get('name')}' 成功存入数据库。")
                return response.data[0]
            return None
        except Exception as e:
            print(f"ERROR: 创建智能体时发生数据库异常: {e}")
            return None

    async def create_agent_from_character_card(self, user_id: str, character_data: Dict[str, Any], image_url: str, spec: str = "chara_card_v2", spec_version: str = "2.0") -> Optional[Dict[str, Any]]:
        """从角色卡数据创建智能体"""
        try:
            # 1. 定义已映射到专用列的字段，用于后续去重
            mapped_keys = {
                "name", "description", "personality", "scenario", "first_mes",
                "mes_example", "creator_notes", "system_prompt",
                "post_history_instructions", "tags", "gender", "voice_name",
                # 即使它们可能不存在于原始卡中，也列出来以防万一
                "image_url", "avatar_url", "is_public"
            }

            # 2. 准备要插入数据库的数据
            agent_data = {
                "user_id": user_id,
                "name": character_data.get("name", "未命名角色"),
                "description": character_data.get("description", ""),
                "personality": character_data.get("personality", ""),
                "scenario": character_data.get("scenario", ""),
                "first_mes": character_data.get("first_mes", ""),
                "mes_example": character_data.get("mes_example", ""),
                "creator_notes": character_data.get("creator_notes", ""),
                "system_prompt": character_data.get("system_prompt", ""),
                "post_history_instructions": character_data.get("post_history_instructions", ""),
                "tags": character_data.get("tags", []),
                "image_url": image_url,  # 修复点 1: 使用传入的URL
                "avatar_url": image_url, # 修复点 1: 使用传入的URL
                "spec": spec,
                "spec_version": spec_version,
                "is_public": True,
                "gender": character_data.get("gender", "other"),
                "voice_name": character_data.get("voice_name", "Kore")
            }

            # 3. 创建一个副本用于存储到 'data' 字段，并移除已映射的键
            supplementary_data = character_data.copy()
            for key in mapped_keys:
                supplementary_data.pop(key, None)
            
            agent_data["data"] = supplementary_data # 修复点 2: 存入去重后的补充数据

            # 4. 执行插入
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agents").insert(agent_data, returning="representation").execute()
            )
            if response.data:
                print(f"SUCCESS: 角色卡 '{character_data.get('name')}' 成功导入数据库。")
                return response.data[0]
            return None
        except Exception as e:
            print(f"ERROR: 导入角色卡时发生数据库异常: {e}")
            return None

    def _generate_default_roleplay_prompt(self, character_data: Dict[str, Any]) -> str:
        """为没有system_prompt的角色卡生成默认的roleplay_prompt"""
        name = character_data.get("name", "角色")
        personality = character_data.get("personality", "")
        scenario = character_data.get("scenario", "")

        prompt = f"你是{name}。"
        if personality:
            prompt += f"\n\n性格特征：{personality}"
        if scenario:
            prompt += f"\n\n场景设定：{scenario}"

        prompt += f"\n\n请始终保持{name}的角色设定，用第一人称进行对话。回复要生动自然，符合角色的性格特点。"

        return prompt

    # ========================================
    # 故事 (模板) 相关操作
    # ========================================
    
    async def get_stories(self, user_id: Optional[str] = None, is_public: Optional[bool] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """获取故事列表"""
        try:
            query = self.supabase.table("stories").select("*")
            if user_id:
                query = query.eq("user_id", user_id)
            if is_public is not None:
                query = query.eq("is_public", is_public)
            query = query.order("created_at", desc=True).limit(limit)
            response = await asyncio.to_thread(lambda: query.execute())
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取故事列表失败: {e}")
            raise e

    async def get_story_by_id(self, story_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取故事详情 (兼容single异常)"""
        try:
            # 首选 single()，精确获取一条记录，性能最佳
            response = await asyncio.to_thread(
                lambda: self.supabase.table("stories").select("*").eq("id", story_id).single().execute()
            )
            if response and response.data:
                return response.data
        except Exception as e:
            # 当 single() 因返回 0 条或多条记录导致 406/404 时降级为 limit(1)
            print(f"WARN: single() 查询故事 {story_id} 失败 ({e})，正回退至 limit(1) 查询以兼容 Supabase 行为变更。")
            try:
                fallback_resp = await asyncio.to_thread(
                    lambda: self.supabase.table("stories").select("*").eq("id", story_id).limit(1).execute()
                )
                return fallback_resp.data[0] if fallback_resp and fallback_resp.data else None
            except Exception as ee:
                print(f"ERROR: get_story_by_id 回退查询仍失败: {ee}")
                return None
        # 如果 single() 成功但未返回数据
        return None

    @retry_on_db_error()
    async def create_story(self, user_id: str, title: str, theme_prompt: str, **kwargs) -> Optional[Dict[str, Any]]:
        """创建新故事"""
        story_data = {"user_id": user_id, "title": title, "theme_prompt": theme_prompt, **kwargs}
        response = await asyncio.to_thread(
            lambda: self.supabase.table("stories").insert(story_data, returning="representation").execute()
        )
        return response.data[0] if response.data else None

    async def get_story_chapters(self, story_id: str) -> List[Dict[str, Any]]:
        """获取故事的所有章节，按章节号排序"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("story_chapters")
                .select("*").eq("story_id", story_id).order("chapter_number", desc=False).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取故事章节列表失败: {e}")
            raise e

    @retry_on_db_error()
    async def create_story_chapter(self, **kwargs) -> Optional[Dict[str, Any]]:
        """创建并保存一个新的故事章节。"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("story_chapters").insert(kwargs, returning="representation").execute()
            )
            if response.data:
                print(f"SUCCESS: 故事章节 '{kwargs.get('title')}' 成功存入数据库。")
                return response.data[0]
            return None
        except Exception as e:
            print(f"ERROR: 创建故事章节时发生数据库异常: {e}")
            return None

    # ========================================
    # 统一对话模型核心操作 (V5)
    # ========================================

    @retry_on_db_error()
    async def create_chat_session(self, user_id: str, agent_ids: List[str], story_id: Optional[str] = None) -> Optional[str]:
        """创建一个新的对话会话，并关联参与者，返回 chat_id"""
        try:
            # 1. 在 chats 表中插入新记录
            chat_insert_data = {"user_id": user_id}
            if story_id:
                chat_insert_data["story_id"] = story_id
            
            chat_response = await asyncio.to_thread(
                lambda: self.supabase.table("chats").insert(chat_insert_data, returning="representation").execute()
            )
            
            if not chat_response.data:
                print("ERROR: 创建chat会话失败，未返回数据。")
                return None
            
            new_chat_id = chat_response.data[0]['id']
            
            # 2. 在 chat_participants 表中为每个 agent_id 插入记录
            if agent_ids:
                participants_data = [{"chat_id": new_chat_id, "agent_id": agent_id} for agent_id in agent_ids]
                participants_response = await asyncio.to_thread(
                    lambda: self.supabase.table("chat_participants").insert(participants_data).execute()
                )
                if participants_response.data is None and participants_response.error:
                     print(f"ERROR: 关联会话参与者失败: {participants_response.error}")
                     # Consider rolling back the chat creation or handling the error
                     return None

            print(f"SUCCESS: 成功创建会话 {new_chat_id}，关联了 {len(agent_ids)} 个参与者。")
            return new_chat_id

        except Exception as e:
            print(f"ERROR: 创建会话时发生严重错误: {e}")
            traceback.print_exc()
            return None

    @retry_on_db_error()
    async def add_message_to_chat(self, chat_id: str, role: str, content: str, agent_id: Optional[str] = None, metadata: Optional[Dict] = None, audio_url: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """向指定会话添加一条新消息"""
        try:
            message_data = {
                "chat_id": chat_id,
                "role": role,
                "content": content,
                "agent_id": agent_id,
                "metadata": metadata,
                "audio_url": audio_url
            }
            response = await asyncio.to_thread(
                lambda: self.supabase.table("messages").insert(message_data, returning="representation").execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"ERROR: 添加消息到会话 {chat_id} 失败: {e}")
            return None

    @retry_on_db_error()
    async def update_message_embedding(self, message_id: int, embedding: List[float]) -> bool:
        """更新消息的向量表示"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("messages")
                .update({"embedding": embedding})
                .eq("id", message_id)
                .execute()
            )
            if response.data:
                print(f"SUCCESS: 消息 {message_id} 的embedding更新成功")
                return True
            return False
        except Exception as e:
            print(f"ERROR: 更新消息 {message_id} 的embedding失败: {e}")
            return False

    @retry_on_db_error()
    async def search_chat_memories(self, chat_id: str, query_embedding: List[float], match_threshold: float = 0.78, match_count: int = 5) -> List[Dict[str, Any]]:
        """使用向量相似度搜索聊天记忆"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc(
                    "match_chat_memories",
                    {
                        "query_embedding": query_embedding,
                        "p_chat_id": chat_id,
                        "match_threshold": match_threshold,
                        "match_count": match_count
                    }
                ).execute()
            )
            memories = response.data or []
            print(f"SUCCESS: 在会话 {chat_id} 中找到 {len(memories)} 条相关记忆")
            return memories
        except Exception as e:
            print(f"ERROR: 搜索会话 {chat_id} 的记忆失败: {e}")
            raise e

    async def get_messages_by_chat_id(self, chat_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """分页获取指定会话的历史消息"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("messages")
                .select("*")
                .eq("chat_id", chat_id)
                .order("created_at", desc=False)
                .range(offset, offset + limit - 1)
                .execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取会话 {chat_id} 的消息失败: {e}")
            raise e

    async def get_chat_participants(self, chat_id: str) -> List[Dict[str, Any]]:
        """获取指定会话的所有AI参与者信息"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chat_participants")
                .select("agents(*)")
                .eq("chat_id", chat_id)
                .execute()
            )
            return [p['agents'] for p in response.data if p.get('agents')] if response.data else []
        except Exception as e:
            print(f"ERROR: 获取会话 {chat_id} 的参与者失败: {e}")
            raise e

    async def get_user_chat_list(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取用户的聊天列表 (临时使用简化查询)"""
        try:
            # 临时使用简化的查询，直接从chats表获取数据
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chats")
                .select("""
                    id,
                    story_id,
                    updated_at,
                    chat_participants!inner(
                        agents!inner(
                            id,
                            name,
                            image_url
                        )
                    )
                """)
                .eq("user_id", user_id)
                .order("updated_at", desc=True)
                .limit(limit)
                .execute()
            )

            # 转换为前端期望的格式
            chat_list = []
            for chat in response.data or []:
                # 获取第一个参与者作为显示信息
                participants = chat.get('chat_participants', [])
                if participants:
                    agent = participants[0].get('agents', {})
                    display_name = agent.get('name', '未知角色')
                    display_avatar = agent.get('image_url')
                else:
                    display_name = '未知对话'
                    display_avatar = None

                chat_item = {
                    'chat_id': chat['id'],
                    'display_name': display_name,
                    'display_avatar': display_avatar,
                    'latest_message': None,  # 暂时为空
                    'latest_message_time': None,  # 暂时为空
                    'unread_count': 0,
                    'level': 1,
                    'relationship_status': None,
                    'event_tag': None,
                }
                chat_list.append(chat_item)

            return chat_list

        except Exception as e:
            print(f"ERROR: 获取用户聊天列表失败: {e}")
            raise e

    async def get_chat_by_id(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取单个会话的信息"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chats").select("*").eq("id", chat_id).single().execute()
            )
            return response.data
        except Exception as e:
            print(f"ERROR: 获取会话 {chat_id} 失败: {e}")
            return None

    async def get_chat_by_user_and_story(self, user_id: str, story_id: str) -> Optional[Dict[str, Any]]:
        """根据用户ID和故事ID查找会话（若存在则返回）"""
        try:
            # 修复：使用 limit(1) 而不是 maybe_single() 来避免 Missing response 错误
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chats")
                .select("*")
                .eq("user_id", user_id)
                .eq("story_id", story_id)
                .order("updated_at", desc=True)  # 按更新时间降序，获取最新的会话
                .limit(1)
                .execute()
            )
            # 如果找到记录，返回第一条；否则返回 None
            return response.data[0] if response and response.data and len(response.data) > 0 else None
        except Exception as e:
            print(f"ERROR: 获取用户 {user_id} 的故事 {story_id} 会话失败: {e}")
            return None

    async def get_chat_by_user_and_agent(self, user_id: str, agent_id: str) -> Optional[Dict[str, Any]]:
        """根据用户ID和角色ID查找会话（若存在则返回）"""
        try:
            # 修复：使用 limit(1) 而不是 maybe_single() 来避免 Missing response 错误
            # 修复：使用 is_("story_id", "null") 而不是 eq("story_id", None) 来查询NULL值
            response = await asyncio.to_thread(
                lambda: self.supabase.table("chats")
                .select("*, chat_participants!inner(agent_id)")
                .eq("user_id", user_id)
                .is_("story_id", "null")  # 修复：正确查询NULL值
                .eq("chat_participants.agent_id", agent_id)
                .order("updated_at", desc=True)  # 按更新时间降序，获取最新的会话
                .limit(1)
                .execute()
            )
            # 如果找到记录，返回第一条；否则返回 None
            return response.data[0] if response and response.data and len(response.data) > 0 else None
        except Exception as e:
            print(f"ERROR: 获取用户 {user_id} 与角色 {agent_id} 的会话失败: {e}")
            return None

    @retry_on_db_error()
    async def update_chat_progress(self, chat_id: str, progress_data: Dict) -> bool:
        """更新会话的任务进度"""
        try:
            await asyncio.to_thread(
                lambda: self.supabase.table("chats").update({"task_progress": progress_data}).eq("id", chat_id).execute()
            )
            return True
        except Exception as e:
            print(f"ERROR: 更新会话 {chat_id} 进度失败: {e}")
            return False

    @retry_on_db_error()
    async def update_chat_game_state(self, chat_id: str, game_state: Dict) -> bool:
        """织梦者引擎：更新会话的游戏状态"""
        try:
            await asyncio.to_thread(
                lambda: self.supabase.table("chats").update({"game_state": game_state}).eq("id", chat_id).execute()
            )
            return True
        except Exception as e:
            print(f"ERROR: 更新会话 {chat_id} 游戏状态失败: {e}")
            return False

    async def touch_chat(self, chat_id: str) -> bool:
        """更新指定会话的 updated_at 时间戳"""
        try:
            # 调用 update 方法但不传递任何数据，这样只会触发 update_updated_at_column 触发器
            await asyncio.to_thread(
                lambda: self.supabase.table("chats").update({}).eq("id", chat_id).execute()
            )
            return True
        except Exception as e:
            print(f"ERROR: 'Touching' chat {chat_id} failed: {e}")
            return False

    # ========================================
    # 记忆相关操作
    # ========================================
    async def create_agent_memory(self, agent_id: str, user_id: str, memory_text: str) -> Optional[Dict[str, Any]]:
        """创建智能体记忆"""
        try:
            memory_data = {"agent_id": agent_id, "user_id": user_id, "memory_text": memory_text}
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agent_memories").insert(memory_data, returning="representation").execute()
            )
            return response.data[0] if response.data else None
        except Exception as e:
            print(f"ERROR: 创建记忆失败: {e}")
            return None

    async def get_agent_memories(self, agent_id: str, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取智能体记忆列表"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("agent_memories")
                .select("*").eq("agent_id", agent_id).eq("user_id", user_id)
                .order("created_at", desc=True).limit(limit).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取记忆列表失败: {e}")
            return []

    # ========================================
    # 排行榜、搜索等辅助功能
    # ========================================
    async def get_story_rankings(self, period: str) -> List[Dict[str, Any]]:
        """获取故事排行榜"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc('get_story_rankings', {'p_period': period}).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取故事排行榜失败: {e}")
            raise e

    async def get_agent_rankings(self, period: str) -> List[Dict[str, Any]]:
        """获取角色排行榜"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc('get_agent_rankings', {'p_period': period}).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取角色排行榜失败: {e}")
            raise e

    # ========================================
    # 首页推荐 & 发现 - 辅助方法
    # ========================================

    async def get_public_agents_with_creator(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取公开的智能体列表，并附带创作者信息 (使用数据库RPC)"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.rpc('get_public_agents_with_creator', {'p_limit': limit}).execute()
            )
            return response.data or []
        except Exception as e:
            print(f"ERROR: 获取公开智能体列表失败: {e}")
            raise e
            
    async def search_agents_and_users(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """同时搜索智能体和用户"""
        try:
            search_term = f"%{query}%"
            agents_task = asyncio.to_thread(
                lambda: self.supabase.table("agents").select("*").ilike("name", search_term).limit(limit).execute()
            )
            users_task = asyncio.to_thread(
                lambda: self.supabase.table("user_profiles").select("id, user_id, display_name, avatar_url").ilike("display_name", search_term).limit(limit).execute()
            )
            agents_response, users_response = await asyncio.gather(agents_task, users_task)
            return {"agents": agents_response.data or [], "users": users_response.data or []}
        except Exception as e:
            print(f"ERROR: 搜索失败: {e}")
            raise e

    # ========================================
    # ========================================
    # Agent模式配置管理
    # ========================================
    @retry_on_db_error()
    async def create_agent_mode_config(
        self,
        agent_id: str,
        mode: str,
        mode_specific_first_mes: Optional[str] = None,
        mode_specific_scenario: Optional[str] = None,
        mode_specific_instructions: Optional[str] = None,
        enable_mes_example: bool = True,
        enable_backstory: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        创建Agent模式特定配置

        Args:
            agent_id: Agent ID
            mode: 模式 ('chat' 或 'story')
            mode_specific_first_mes: 模式特定开场白
            mode_specific_scenario: 模式特定场景
            mode_specific_instructions: 模式特定指令
            enable_mes_example: 是否启用对话示例
            enable_backstory: 是否启用背景故事

        Returns:
            创建的配置记录，失败时返回None
        """
        try:
            config_data = {
                'agent_id': agent_id,
                'mode': mode,
                'enable_mes_example': enable_mes_example,
                'enable_backstory': enable_backstory
            }

            # 只添加非空的可选字段
            if mode_specific_first_mes:
                config_data['mode_specific_first_mes'] = mode_specific_first_mes
            if mode_specific_scenario:
                config_data['mode_specific_scenario'] = mode_specific_scenario
            if mode_specific_instructions:
                config_data['mode_specific_instructions'] = mode_specific_instructions

            response = await asyncio.to_thread(
                lambda: self.supabase.table('agent_mode_configs').insert(config_data).execute()
            )

            if response.data:
                print(f"SUCCESS: Agent模式配置创建成功 - Agent: {agent_id}, Mode: {mode}")
                return response.data[0]
            else:
                print(f"ERROR: Agent模式配置创建失败 - 无数据返回")
                return None

        except Exception as e:
            print(f"ERROR: Agent模式配置创建失败: {e}")
            return None

    # ========================================
    # 健康检查
    # ========================================
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = await asyncio.to_thread(lambda: self.supabase.rpc('health_check', {}).execute())
            if response.data is not None:
                return {"status": "healthy", "database": "connected"}
            else:
                raise Exception(f"Health check RPC call returned unexpected data: {response.data}")
        except Exception as e:
            print(f"ERROR: 数据库健康检查失败: {e}")
            return {"status": "unhealthy", "database": "disconnected", "error": str(e)}

# 创建全局服务实例
supabase_service = SupabaseService()