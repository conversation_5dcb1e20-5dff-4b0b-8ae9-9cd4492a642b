import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../common/widgets/glassmorphism_container.dart';

class MainShell extends StatefulWidget {
  final StatefulNavigationShell navigationShell;

  const MainShell({super.key, required this.navigationShell});

  @override
  State<MainShell> createState() => _MainShellState();
}

class _MainShellState extends State<MainShell> {
  void _onItemTapped(int index) {
    // 中央的"+"按钮 (UI index 2) 有它自己的路由逻辑，不通过goBranch处理
    if (index == 2) {
      context.push('/create-agent'); // 直接跳转到创建页
      return;
    }

    // 将UI索引映射到正确的StatefulNavigationShell分支索引
    // UI索引 0, 1 -> 分支 0, 1
    // UI索引 3, 4 -> 分支 2, 3
    final int branchIndex = index > 2 ? index - 1 : index;

    widget.navigationShell.goBranch(
      branchIndex,
      initialLocation: branchIndex == widget.navigationShell.currentIndex,
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final int branchIndex = widget.navigationShell.currentIndex;
    // 将分支索引反向映射回UI索引
    // 分支 0, 1 -> UI 0, 1
    // 分支 2, 3 -> UI 3, 4
    if (branchIndex >= 2) {
      return branchIndex + 1;
    }
    return branchIndex;
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true, // 关键：让body延伸到导航栏下方
      backgroundColor: AppColors.background, // 主背景色
      body: widget.navigationShell,
      bottomNavigationBar: _buildCustomBottomNav(), // 使用自定义导航栏
    );
  }

  /// 构建自定义底部导航栏（带玻璃拟态效果）
  Widget _buildCustomBottomNav() {
    return GlassmorphismStyles.navigationBar(
      child: SizedBox(
        height: 83, // 包含安全区的高度
        child: SafeArea(
          top: false, // 关键：只应用底部安全区
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: <Widget>[
              // 明确传递UI索引
              _buildNavItem(icon: Icons.chat_bubble_outline, label: '聊天', index: 0),
              _buildNavItem(icon: Icons.favorite_border, label: '羁绊', index: 1),
              _buildCreationButton(), // 这个是索引2
              _buildNavItem(icon: Icons.explore_outlined, label: '推荐', index: 3),
              _buildNavItem(icon: Icons.person_outline, label: '我的', index: 4),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建普通导航项（带辉光和弹性动画）
  Widget _buildNavItem({required IconData icon, required String label, required int index}) {
    final selectedUiIndex = _calculateSelectedIndex(context);
    final isSelected = selectedUiIndex == index;
    // **这里的 _onItemTapped 调用现在传递的是正确的UI索引**
    return Expanded(
      child: InkWell(
        onTap: () => _onItemTapped(index),
        borderRadius: BorderRadius.circular(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 辉光和弹性动画容器
            TweenAnimationBuilder<double>(
              tween: Tween(begin: 1.0, end: isSelected ? 1.2 : 1.0),
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeOutBack,
              builder: (context, scale, child) {
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    decoration: isSelected
                        ? BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.accentPurple.withOpacity(0.7),
                                blurRadius: 15.0,
                                spreadRadius: 2.0,
                              ),
                            ],
                          )
                        : null,
                    child: child,
                  ),
                );
              },
              child: Icon(
                icon,
                color: isSelected ? AppColors.accentPurple : AppColors.secondaryText,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? AppColors.accentPurple : AppColors.secondaryText,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建自定义创作按钮（带渐变和立体感）
  Widget _buildCreationButton() {
    return InkWell(
      onTap: () => _onItemTapped(2),
      child: Container(
        width: 48,
        height: 32,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: AppColors.primaryGradient,
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1.0,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.accentPurple.withOpacity(0.5),
              blurRadius: 8.0,
              spreadRadius: 1.0,
            ),
          ],
        ),
        child: const Icon(
          Icons.add,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }
} 
