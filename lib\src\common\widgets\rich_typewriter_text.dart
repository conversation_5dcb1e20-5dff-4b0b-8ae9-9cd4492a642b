import 'dart:async';
import 'package:flutter/material.dart';

/// 支持富文本格式的打字机文本组件
/// 
/// 能够在打字机动画过程中正确处理括号内容的特殊样式
class RichTypewriterText extends StatefulWidget {
  final String text;
  final TextStyle style;
  final Duration duration;
  final bool skipAnimation;
  final bool forceComplete;

  const RichTypewriterText(
    this.text, {
    super.key,
    required this.style,
    this.duration = const Duration(milliseconds: 50),
    this.skipAnimation = false,
    this.forceComplete = false,
  });

  @override
  State<RichTypewriterText> createState() => _RichTypewriterTextState();
}

class _RichTypewriterTextState extends State<RichTypewriterText> {
  String _displayedText = "";
  Timer? _timer;
  int _currentIndex = 0;
  List<InlineSpan> _currentSpans = [];

  @override
  void initState() {
    super.initState();
    if (widget.skipAnimation || widget.forceComplete) {
      _displayedText = widget.text;
      _currentIndex = widget.text.length;
      _currentSpans = _buildFormattedSpans(widget.text);
    } else {
      _startTyping();
    }
  }

  @override
  void didUpdateWidget(RichTypewriterText oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.forceComplete && !oldWidget.forceComplete) {
      _timer?.cancel();
      setState(() {
        _displayedText = widget.text;
        _currentIndex = widget.text.length;
        _currentSpans = _buildFormattedSpans(widget.text);
      });
      return;
    }

    if (widget.text != oldWidget.text) {
      if (widget.skipAnimation || widget.forceComplete) {
        setState(() {
          _displayedText = widget.text;
          _currentIndex = widget.text.length;
          _currentSpans = _buildFormattedSpans(widget.text);
        });
      } else {
        if (widget.text.startsWith(_displayedText) && widget.text.length > _displayedText.length) {
          _continueTyping();
        } else {
          _resetAndStart();
        }
      }
    }
  }

  void _startTyping() {
    _timer?.cancel();
    _currentIndex = 0;
    _displayedText = "";
    _currentSpans = [];
    _continueTyping();
  }
  
  void _continueTyping() {
    _timer?.cancel();
    _timer = Timer.periodic(widget.duration, (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      if (_currentIndex < widget.text.length) {
        setState(() {
          _currentIndex++;
          _displayedText = widget.text.substring(0, _currentIndex);
          _currentSpans = _buildFormattedSpans(_displayedText);
        });
      } else {
        timer.cancel();
      }
    });
  }
  
  void _resetAndStart() {
    if(mounted) {
      setState(() {
        _currentIndex = 0;
        _displayedText = "";
        _currentSpans = [];
      });
    }
    _startTyping();
  }

  /// 构建富文本格式的文本片段
  List<InlineSpan> _buildFormattedSpans(String text) {
    final List<InlineSpan> spans = [];
    String processedText = text.replaceAll(RegExp(r'["""'']'), '');
    final RegExp regExp = RegExp(r'([（(][^）)]*[）)])');

    processedText.splitMapJoin(
      regExp,
      onMatch: (Match match) {
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontStyle: FontStyle.italic,
            fontSize: widget.style.fontSize ?? 16,
            height: widget.style.height ?? 1.5,
          ),
        ));
        return '';
      },
      onNonMatch: (String nonMatch) {
        if (nonMatch.trim().isNotEmpty) {
          spans.add(TextSpan(
            text: nonMatch,
            style: widget.style,
          ));
        }
        return '';
      },
    );

    return spans;
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        children: _currentSpans,
        style: widget.style,
      ),
    );
  }
}
