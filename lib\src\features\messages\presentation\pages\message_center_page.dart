// 文件路径: lib/src/features/messages/presentation/pages/message_center_page.dart
import 'dart:ui';
import 'package:flutter/material.dart'; // <--- 添加这行
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart'; // <--- 添加这行
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../bloc/message_center_bloc.dart'; // 正确的路径
import '../../../auth/bloc/auth_bloc.dart';
import '../../models/chat_list_item.dart';
import '../../repository/message_repository.dart';

class MessageCenterPage extends StatelessWidget {
  const MessageCenterPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 移除 DefaultTabController 和原有的 TabBar，改为竞品的聊天页面布局
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: const Text(
          '聊天',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryText,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.people_alt_outlined, color: AppColors.primaryText),
            onPressed: () {
              // TODO: 跳转到群聊或好友列表
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_horiz, color: AppColors.primaryText),
            onPressed: () {
              // TODO: 显示更多选项
            },
          ),
        ],
      ),
      body: BlocProvider(
        create: (context) => MessageCenterBloc(
          repository: RepositoryProvider.of<MessageRepository>(context),
          authBloc: BlocProvider.of<AuthBloc>(context),
        )..add(LoadChatList()),
        child: Stack(
          children: [
            Column(
              children: [
                _buildEventNotifications(),
                _buildFriendUpdates(),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      '好友消息',
                      style: TextStyle(
                        color: AppColors.primaryText,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: BlocBuilder<MessageCenterBloc, MessageCenterState>(
                    builder: (context, state) {
                      if (state is MessageCenterLoading) {
                        return const Center(child: CircularProgressIndicator(color: AppColors.accentPurple));
                      }
                      if (state is MessageCenterLoaded) {
                        if (state.chatList.isEmpty) {
                          return const Center(
                            child: Text(
                              '暂无消息，快去和TA打个招呼吧',
                              style: TextStyle(color: AppColors.secondaryText),
                            ),
                          );
                        }
                        return _buildDynamicMessagesList(state.chatList);
                      }
                      if (state is MessageCenterError) {
                        return Center(
                          child: Text(
                            state.message,
                            style: const TextStyle(color: Colors.red),
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ],
            ),
            _buildBottomBanner(), // 底部横幅
          ],
        ),
      ),
    );
  }

  Widget _buildEventNotifications() {
    // 模拟数据
    final notifications = [
      {'title': '容身之处', 'subtitle': '初次见面，继兄将我堵在门口……', 'icon': Icons.home_work_outlined, 'color': AppColors.accentBlue, 'tag': '新关系'},
      {'title': '抢亲', 'subtitle': '把你从花轿上抢过来，你肯定恨极了我吧。', 'icon': Icons.favorite_border, 'color': AppColors.accentPurple, 'tag': '新事件'},
      {'title': '私藏心事', 'subtitle': '一个眼神的交汇，足以让他所有的伪装瞬间瓦解。', 'icon': Icons.lock_outline, 'color': AppColors.accentYellow, 'tag': '新事件'},
    ];

    return Container(
      height: 90, // 设定一个固定高度
      margin: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          final item = notifications[index];
          return _buildEventCard(
            title: item['title'] as String,
            subtitle: item['subtitle'] as String,
            icon: item['icon'] as IconData,
            color: item['color'] as Color,
            tag: item['tag'] as String,
          );
        },
      ),
    );
  }

  // 将 _buildEventCard 修改为竞品样式
  Widget _buildEventCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required String tag,
  }) {
    return Container(
      width: 220, // 卡片宽度
      margin: const EdgeInsets.only(right: 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.0),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.secondaryBg.withOpacity(0.5),
              borderRadius: BorderRadius.circular(16.0),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 12,
                      backgroundColor: color.withOpacity(0.2),
                      child: Icon(icon, color: color, size: 14),
                    ),
                    const SizedBox(width: 8),
                    Text(title, style: const TextStyle(color: AppColors.primaryText, fontWeight: FontWeight.bold, fontSize: 14)),
                    const Spacer(),
                    Text(tag, style: TextStyle(color: color, fontSize: 10, fontWeight: FontWeight.bold)),
                  ],
                ),
                const Spacer(),
                Text(
                  subtitle,
                  style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 新增方法：构建好友动态更新
  Widget _buildFriendUpdates() {
    final friendUpdates = [
      {'name': '小雪', 'status': '吃醋', 'avatar': 'https://i.imgur.com/avatar1.png'},
      {'name': '小雨', 'status': '冷落', 'avatar': 'https://i.imgur.com/avatar2.png'},
      {'name': '小风', 'status': '想念', 'avatar': 'https://i.imgur.com/avatar3.png'},
      {'name': '小月', 'status': '开心', 'avatar': 'https://i.imgur.com/avatar4.png'},
    ];

    return Container(
      height: 80,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: friendUpdates.length,
        itemBuilder: (context, index) {
          final friend = friendUpdates[index];
          return Container(
            width: 60,
            margin: const EdgeInsets.only(right: 12),
            child: Column(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 24,
                      backgroundImage: NetworkImage(friend['avatar']!),
                      onBackgroundImageError: (_, __) {},
                      child: friend['avatar']!.isEmpty
                          ? const Icon(Icons.person, color: AppColors.secondaryText)
                          : null,
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getStatusColor(friend['status']!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          friend['status']!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  friend['name']!,
                  style: const TextStyle(
                    color: AppColors.primaryText,
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case '吃醋':
        return Colors.red;
      case '冷落':
        return Colors.blue;
      case '想念':
        return Colors.purple;
      case '开心':
        return Colors.green;
      default:
        return AppColors.accentPurple;
    }
  }

  // 新增方法：构建底部横幅
  Widget _buildBottomBanner() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(25),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.notifications_active_outlined,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Expanded(
              child: Text(
                '是否要接收TA的吃醋、冷落消息~',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // TODO: 处理接受逻辑
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accentPurple,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              ),
              child: const Text(
                '接受',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            IconButton(
              onPressed: () {
                // TODO: 处理关闭逻辑
              },
              icon: const Icon(Icons.close, color: Colors.white, size: 20),
            ),
          ],
        ),
      ),
    );
  }

  // 构建动态消息列表
  Widget _buildDynamicMessagesList(List<ChatListItem> chatList) {
    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 100), // 为底部横幅留出空间
      itemCount: chatList.length,
      itemBuilder: (context, index) {
        final chat = chatList[index];
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0), // 增加垂直间距
          onTap: () => context.push('/chat/${chat.chatId}'),
          leading: CircleAvatar(
            radius: 28, // 增大头像尺寸
            backgroundImage: (chat.displayAvatar != null && chat.displayAvatar!.isNotEmpty)
                ? NetworkImage(chat.displayAvatar!)
                : null,
            child: (chat.displayAvatar == null || chat.displayAvatar!.isEmpty)
                ? const Icon(Icons.person)
                : null,
          ),
          title: Row(
            children: [
              Text(
                chat.displayName,
                style: AppTextStyles.body.copyWith(color: AppColors.primaryText, fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 8),
              // --- 等级标签 ---
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.accentPurple.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Lv.${chat.level}',
                  style: const TextStyle(color: AppColors.accentPurple, fontSize: 10, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          subtitle: Text(
            chat.latestMessage ?? '...',
            style: AppTextStyles.bodySecondary.copyWith(color: AppColors.secondaryText, fontSize: 14),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (chat.latestMessageTime != null)
                Text(
                  _formatTimestamp(chat.latestMessageTime!),
                  style: AppTextStyles.caption.copyWith(fontSize: 12),
                ),
              if (chat.unreadCount > 0) ...[
                const SizedBox(height: 6),
                // --- 未读数角标 ---
                Container(
                  padding: const EdgeInsets.all(5),
                  decoration: const BoxDecoration(
                    color: Colors.pink,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    chat.unreadCount.toString(),
                    style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  // 时间格式化工具方法
  String _formatTimestamp(String isoString) {
    final now = DateTime.now();
    final time = DateTime.parse(isoString).toLocal();
    final difference = now.difference(time);

    if (difference.inDays == 0 && now.day == time.day) {
      return DateFormat('HH:mm').format(time); // 今天
    } else if (difference.inDays == 1 || (difference.inDays == 0 && now.day != time.day)) {
      return '昨天';
    } else {
      return DateFormat('M/d').format(time); // 更早
    }
  }
}
