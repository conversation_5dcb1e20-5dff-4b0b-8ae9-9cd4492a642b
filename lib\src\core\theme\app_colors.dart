// lib/src/core/theme/app_colors.dart

import 'package:flutter/material.dart';

// PRD 规范中的颜色定义 - V3 (深邃星空紫主题)
class AppColors {
  // --- 基础色 (更新为深邃星空紫主题) ---
  static const Color background = Color(0xFF1A182B); // 深邃星空紫 (主背景色)
  static const Color secondaryBg = Color(0xFF2C2A4A); // 组件/卡片背景
  static const Color inputBackground = Color(0xFF211F3A); // 输入框背景

  // --- 文本色 (保持不变) ---
  static const Color primaryText = Color(0xFFFFFFFF);   // 主要文字
  static const Color secondaryText = Color(0xFFA9A8B3); // 次要/提示文字
  static const Color tertiaryText = Color(0xFF6F6E7B);  // 最次要文字

  // --- 强调色 (核心！) ---
  static const Color accentPurple = Color(0xFFC974FF); // 霓虹粉紫 (核心高光)
  static const Color accentYellow = Color(0xFFFFD700); // 金色/黄色 (用于VIP/特殊按钮)
  static const Color accentGreen = Color(0xFF4CAF50);  // 绿色 (用于成功、完成状态)
  static const Color accentBlue = Color(0xFF2196F3);   // 蓝色 (用于链接、特殊标签)
  static const Color accentOrange = Color(0xFFFF9800); // 橙色 (用于警告、特殊标签)

  // --- 渐变色 ---
  static const Gradient primaryGradient = LinearGradient(
    colors: [Color(0xFF8A4FFF), Color(0xFFE56FFF)],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );

  // --- 背景渐变色 (核心！) ---
  static const Color primaryGradientStart = Color(0xFF2A1B3D); // 深邃紫 (渐变起始)
  static const Color primaryGradientEnd = Color(0xFF44318D);   // 明亮紫 (渐变结束)

  // --- 辉光/特效色 (核心！) ---
  static const Color glowPurple = Color(0x99E0B0FF); // 紫色辉光 (半透明)

  // --- 卡片背景色 ---
  static const Color cardBackground = Color(0xFF2C2C2E); // 卡片背景色
}
