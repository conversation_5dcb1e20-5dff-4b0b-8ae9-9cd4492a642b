遵命。作为顶级前端开发专家，我将严格遵循产品经理提出的每一项优化建议，提供具体、可执行的代码修改方案。我的方案将明确指出需要修改的文件路径、定位到需要更改的代码块，并提供可以直接替换的最终代码。

在开始之前，我们必须首先解决导致应用核心功能瘫痪的**P0级导航BUG**。

---

### **P0级优先事项：导航BUG修复**

**问题根源：** `StatefulNavigationShell` 的 `branch` 索引与底部导航栏UI的 `item` 索引不匹配。UI上有5个可点击区域（4个Tab + 1个中央按钮），但 `StatefulNavigationShell` 只有4个 `branches`。

**解决方案：** 修改 `MainShell` 以正确处理UI索引到逻辑分支索引的映射。

#### **文件: `lib/src/features/main/presentation/pages/main_shell.dart`**

**1. 定位到 `_MainShellState` 类**

**2. 找到并替换 `_onItemTapped` 和 `_calculateSelectedIndex` 方法：**

```dart
// 找到这两个方法:
// void _onItemTapped(int index) { ... }
// int _calculateSelectedIndex(BuildContext context) { ... }

// 用以下代码替换它们:
  void _onItemTapped(int index) {
    // 中央的“+”按钮 (UI index 2) 有它自己的路由逻辑，不通过goBranch处理
    if (index == 2) {
      context.push('/create-agent'); // 直接跳转到创建页
      return;
    }

    // 将UI索引映射到正确的StatefulNavigationShell分支索引
    // UI索引 0, 1 -> 分支 0, 1
    // UI索引 3, 4 -> 分支 2, 3
    final int branchIndex = index > 2 ? index - 1 : index;

    widget.navigationShell.goBranch(
      branchIndex,
      initialLocation: branchIndex == widget.navigationShell.currentIndex,
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final int branchIndex = widget.navigationShell.currentIndex;
    // 将分支索引反向映射回UI索引
    // 分支 0, 1 -> UI 0, 1
    // 分支 2, 3 -> UI 3, 4
    if (branchIndex >= 2) {
      return branchIndex + 1;
    }
    return branchIndex;
  }
```

**3. 找到并修改 `_buildNavItem` 和 `_buildCustomBottomNav` 方法，确保传递正确的UI索引：**

```dart
// 找到 _buildCustomBottomNav 方法
Widget _buildCustomBottomNav() {
  return GlassmorphismStyles.navigationBar(
    child: SizedBox(
      height: 83, // 包含安全区的高度
      child: SafeArea(
        top: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            // 明确传递UI索引
            _buildNavItem(icon: Icons.chat_bubble_outline, label: '聊天', index: 0),
            _buildNavItem(icon: Icons.favorite_border, label: '羁绊', index: 1),
            _buildCreationButton(), // 这个是索引2
            _buildNavItem(icon: Icons.explore_outlined, label: '推荐', index: 3),
            _buildNavItem(icon: Icons.person_outline, label: '我的', index: 4),
          ],
        ),
      ),
    ),
  );
}

// 找到 _buildNavItem 方法
Widget _buildNavItem({required IconData icon, required String label, required int index}) {
  final selectedUiIndex = _calculateSelectedIndex(context);
  final isSelected = selectedUiIndex == index;
  // **这里的 _onItemTapped 调用现在传递的是正确的UI索引**
  return Expanded(
    child: InkWell(
      onTap: () => _onItemTapped(index),
      borderRadius: BorderRadius.circular(8),
      // ... 动画和UI部分代码保持不变 ...
      // ... (省略未修改部分)
```

**修复完成后，请彻底测试导航功能，确保每个Tab都能正确跳转到对应的页面。**

---

### **1.【聊天】页面 (`MessageCenterPage`) 优化**

**目标：** 复刻竞品丰富的信息层级和情感化设计，用毛玻璃质感提升整体UI。

**前置工作 (致后端/模型工程师):**
请确保 `ChatListItem` 模型 (`lib/src/features/messages/models/chat_list_item.dart`) 已添加以下字段，并且API (`/api/user-chats`) 能够返回这些数据。

```dart
// 文件: lib/src/features/messages/models/chat_list_item.dart
class ChatListItem extends Equatable {
  // ... 其他字段
  final int level; // 新增
  final String? relationshipStatus; // 新增, e.g., '吃醋', '冷落'
  final String? eventTag;           // 新增, e.g., '新事件'
  
  // ... 在构造函数和 fromJson 中添加相应字段
}
```

#### **文件: `lib/src/features/messages/presentation/pages/message_center_page.dart`**

**1. 引入必要的组件和库：**

```dart
// 在文件顶部添加
import 'dart:ui';
import 'package:intl/intl.dart';
```

**2. 替换 `_buildEventNotifications` 方法：**

```dart
// 找到 _buildEventNotifications 方法
// 用以下代码替换

Widget _buildEventNotifications() {
  // 模拟数据
  final notifications = [
    {'title': '容身之处', 'subtitle': '初次见面，继兄将我堵在门口……', 'icon': Icons.home_work_outlined, 'color': AppColors.accentBlue, 'tag': '新关系'},
    {'title': '抢亲', 'subtitle': '把你从花轿上抢过来，你肯定恨极了我吧。', 'icon': Icons.favorite_border, 'color': AppColors.accentPurple, 'tag': '新事件'},
    {'title': '私藏心事', 'subtitle': '一个眼神的交汇，足以让他所有的伪装瞬间瓦解。', 'icon': Icons.lock_outline, 'color': AppColors.accentYellow, 'tag': '新事件'},
  ];

  return Container(
    height: 90, // 设定一个固定高度
    margin: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
    child: ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final item = notifications[index];
        return _buildEventCard(
          title: item['title'] as String,
          subtitle: item['subtitle'] as String,
          icon: item['icon'] as IconData,
          color: item['color'] as Color,
          tag: item['tag'] as String,
        );
      },
    ),
  );
}

// 将 _buildEventCard 修改为竞品样式
Widget _buildEventCard({
  required String title,
  required String subtitle,
  required IconData icon,
  required Color color,
  required String tag,
}) {
  return Container(
    width: 220, // 卡片宽度
    margin: const EdgeInsets.only(right: 12),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16.0),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.secondaryBg.withOpacity(0.5),
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 12,
                    backgroundColor: color.withOpacity(0.2),
                    child: Icon(icon, color: color, size: 14),
                  ),
                  const SizedBox(width: 8),
                  Text(title, style: const TextStyle(color: AppColors.primaryText, fontWeight: FontWeight.bold, fontSize: 14)),
                  const Spacer(),
                  Text(tag, style: TextStyle(color: color, fontSize: 10, fontWeight: FontWeight.bold)),
                ],
              ),
              const Spacer(),
              Text(
                subtitle,
                style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
```

**3. 替换 `_buildDynamicMessagesList` 方法中的 `ListTile`：**

```dart
// 找到 _buildDynamicMessagesList 方法
// 定位到其中的 ListTile 组件

// 用以下代码替换整个 ListTile
return ListTile(
  contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0), // 增加垂直间距
  onTap: () => context.push('/chat/${chat.chatId}'),
  leading: CircleAvatar(
    radius: 28, // 增大头像尺寸
    backgroundImage: (chat.displayAvatar != null && chat.displayAvatar!.isNotEmpty)
        ? NetworkImage(chat.displayAvatar!)
        : null,
    child: (chat.displayAvatar == null || chat.displayAvatar!.isEmpty)
        ? const Icon(Icons.person)
        : null,
  ),
  title: Row(
    children: [
      Text(
        chat.displayName,
        style: AppTextStyles.body.copyWith(color: AppColors.primaryText, fontSize: 16, fontWeight: FontWeight.w500),
      ),
      const SizedBox(width: 8),
      // --- 等级标签 ---
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: AppColors.accentPurple.withOpacity(0.3),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Lv.${chat.level}',
          style: const TextStyle(color: AppColors.accentPurple, fontSize: 10, fontWeight: FontWeight.bold),
        ),
      ),
    ],
  ),
  subtitle: Text(
    chat.latestMessage ?? '...',
    style: AppTextStyles.bodySecondary.copyWith(color: AppColors.secondaryText, fontSize: 14),
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
  ),
  trailing: Column(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.end,
    children: [
      if (chat.latestMessageTime != null)
        Text(
          _formatTimestamp(chat.latestMessageTime!),
          style: AppTextStyles.caption.copyWith(fontSize: 12),
        ),
      if (chat.unreadCount > 0) ...[
        const SizedBox(height: 6),
        // --- 未读数角标 ---
        Container(
          padding: const EdgeInsets.all(5),
          decoration: const BoxDecoration(
            color: Colors.pink,
            shape: BoxShape.circle,
          ),
          child: Text(
            chat.unreadCount.toString(),
            style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    ],
  ),
);```

---

### **2.【羁绊】页面 (`FriendsPage`) 优化**

**目标：** 复刻竞品非对称的功能布局、可折叠的关系列表和信息丰富的角色卡片。

#### **文件: `lib/src/features/friends/presentation/pages/friends_page.dart`**

**1. 替换 `_buildTopFunctionCards` 方法：**

```dart
// 找到 _buildTopFunctionCards 方法
// 用以下代码替换
Widget _buildTopFunctionCards() {
  return Container(
    margin: const EdgeInsets.all(16),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧大卡片
        Expanded(
          flex: 2, // 占据更多空间
          child: _buildFunctionCard(
            title: '拾光之约',
            subtitle: '末显影 那一夜',
            icon: Icons.photo_library,
            color: AppColors.accentYellow,
            isLarge: true, // 新增参数
            onTap: () {},
          ),
        ),
        const SizedBox(width: 12),
        // 右侧两个小卡片
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildFunctionCard(
                title: '留影室',
                subtitle: '留下美好瞬间',
                icon: Icons.camera_alt,
                color: AppColors.accentBlue,
                onTap: () {},
              ),
              const SizedBox(height: 12),
              _buildFunctionCard(
                title: '次元诊疗',
                subtitle: 'TA确诊了肌肤饥渴症',
                icon: Icons.healing,
                color: AppColors.accentPurple,
                onTap: () {},
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

// 修改 _buildFunctionCard 方法以支持不同尺寸和样式
Widget _buildFunctionCard({
  required String title,
  required String subtitle,
  required IconData icon,
  required Color color,
  bool isLarge = false,
  required VoidCallback onTap,
}) {
  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(16),
    child: AspectRatio(
      aspectRatio: isLarge ? 1.0 : 1.0, // 根据大小调整宽高比
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.0),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.secondaryBg.withOpacity(0.5),
              borderRadius: BorderRadius.circular(16.0),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 图标和标题
                Row(
                  children: [
                    Icon(icon, color: Colors.white, size: isLarge ? 24 : 20),
                    const SizedBox(width: 8),
                    Text(title, style: TextStyle(color: AppColors.primaryText, fontSize: isLarge ? 16 : 14, fontWeight: FontWeight.bold)),
                  ],
                ),
                // 副标题
                Text(subtitle, style: TextStyle(color: AppColors.secondaryText, fontSize: isLarge ? 12 : 10)),
              ],
            ),
          ),
        ),
      ),
    ),
  );
}
```

**2. 替换 `_buildRelationshipSection` 方法以使用 `ExpansionTile`：**

```dart
// 找到 _buildRelationshipSection 方法
// 用以下代码替换
Widget _buildRelationshipSection({
  required String title,
  required int levelRequired,
  required List<Map<String, dynamic>> characters,
}) {
  // 注意：这里我们不再使用 _collapsedSections 状态，ExpansionTile自带状态管理
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    decoration: BoxDecoration(
      color: AppColors.secondaryBg,
      borderRadius: BorderRadius.circular(12),
    ),
    child: ExpansionTile(
      // 默认展开 "初见" 分组
      initiallyExpanded: title == '初见',
      title: Row(
        children: [
          Text(title, style: const TextStyle(color: AppColors.primaryText, fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Text('羁绊值Lv.$levelRequired', style: const TextStyle(color: AppColors.secondaryText, fontSize: 12)),
        ],
      ),
      iconColor: AppColors.secondaryText,
      collapsedIconColor: AppColors.secondaryText,
      children: [
        const Divider(color: AppColors.inputBackground, height: 1),
        Padding(
          padding: const EdgeInsets.all(16),
          // 这里使用 Column + for 循环，而不是 GridView，以避免嵌套滚动问题
          child: Column(
            children: [
              for (var char in characters) ...[
                _buildCharacterCard(char),
                const SizedBox(height: 12),
              ]
            ],
          ),
        ),
      ],
    ),
  );
}
```

**3. 彻底重构 `_buildCharacterCard` 方法：**

```dart
// 找到 _buildCharacterCard 方法
// 用以下代码替换
Widget _buildCharacterCard(Map<String, dynamic> character) {
  final bondProgress = (character['bondProgress'] as int).toDouble();
  final maxBond = (character['maxBond'] as int? ?? 100).toDouble(); // 假设最大值为100
  final unlockProgress = bondProgress / maxBond;

  return InkWell(
    onTap: () {
      if (character['isUnlocked']) {
        context.push('/chat/${character['chatId']}');
      }
    },
    borderRadius: BorderRadius.circular(12),
    child: Container(
      height: 120, // 固定高度
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.inputBackground,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // 左侧立绘
          AspectRatio(
            aspectRatio: 1.0,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(character['avatar'], fit: BoxFit.cover),
            ),
          ),
          const SizedBox(width: 12),
          // 右侧信息区
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 名称和等级
                Row(
                  children: [
                    Text(character['name'], style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                    const SizedBox(width: 6),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.accentPurple.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text('Lv.${character['level']}', style: const TextStyle(color: AppColors.accentPurple, fontSize: 10)),
                    )
                  ],
                ),
                // 羁绊值进度条
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('羁绊值 ${bondProgress.toInt()}/${maxBond.toInt()}', style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: unlockProgress,
                      backgroundColor: Colors.white.withOpacity(0.2),
                      valueColor: const AlwaysStoppedAnimation<Color>(AppColors.accentPurple),
                    ),
                  ],
                ),
                // 解锁状态和操作按钮
                Row(
                  children: [
                    Icon(Icons.lock_open, size: 12, color: AppColors.secondaryText),
                    const SizedBox(width: 4),
                    Text(character['unlockDescription'], style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                    const Spacer(),
                    Container(
                      height: 28,
                      decoration: BoxDecoration(
                        gradient: AppColors.primaryGradient,
                        borderRadius: BorderRadius.circular(14),
                      ),
                      child: ElevatedButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.chat_bubble, size: 12, color: Colors.white),
                        label: const Text('邀请TA', style: TextStyle(fontSize: 10, color: Colors.white)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                        ),
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
```

---

### **3.【推荐】页面 (`WatchPage`) 优化**

**目标：** 修复BUG，并将UI元素复刻为竞品的高级样式。

#### **文件: `lib/src/features/home/<USER>/pages/watch_page.dart`**

**1. 替换 `_buildSearchBar` 方法：**

```dart
// 找到 _buildSearchBar 方法
// 用以下代码替换
Widget _buildSearchBar() {
  return GestureDetector(
    onTap: () => context.push('/search'),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(18),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: AppColors.inputBackground.withOpacity(0.5),
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: Row(
            children: [
              const Icon(Icons.search, color: AppColors.secondaryText, size: 20),
              const SizedBox(width: 8),
              const Expanded(
                child: Text('搜索角色、剧情...', style: TextStyle(color: AppColors.secondaryText, fontSize: 14)),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
```

**2. 替换 `_buildFunctionEntries` 方法：**

```dart
// 找到 _buildFunctionEntries 方法
// 用以下代码替换
Widget _buildFunctionEntries() {
  final functions = [
    {'title': '宿命回响', 'subtitle': '多个TA同时回应你', 'icon': Icons.auto_awesome},
    {'title': '印象匹配', 'subtitle': '千种印象任你选', 'icon': Icons.favorite},
    {'title': '自由创建', 'subtitle': '自定义你的专属恋人', 'icon': Icons.create},
    {'title': '每日限免', 'subtitle': '0点刷新', 'icon': Icons.access_time},
  ];

  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: functions.map((func) {
        return Column(
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [AppColors.accentPurple.withOpacity(0.8), AppColors.accentBlue.withOpacity(0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Icon(func['icon'] as IconData, color: Colors.white),
            ),
            const SizedBox(height: 8),
            Text(func['title'] as String, style: const TextStyle(color: AppColors.primaryText, fontSize: 14, fontWeight: FontWeight.w500)),
            const SizedBox(height: 2),
            Text(func['subtitle'] as String, style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
          ],
        );
      }).toList(),
    ),
  );
}
```

**3. 替换 `_buildRecommendationCard` 方法：**

```dart
// 找到 _buildRecommendationCard 方法
// 用以下代码替换
Widget _buildRecommendationCard(RecommendationItem item) {
    // ... (数据提取部分保持不变)
    String title = '未知内容';
    String? coverUrl;
    List<String> tags = [];
    String popularity = '0';

    if (item.type == RecommendationItemType.agent) {
        final agent = item.data as Agent;
        title = agent.name;
        coverUrl = agent.imageUrl;
        tags = agent.tags;
        popularity = agent.dialogueCountFormatted;
    } else {
        final story = item.data as InteractiveStoryCard;
        title = story.title;
        coverUrl = story.coverUrl;
        tags = []; // 故事暂无标签
        popularity = _formatCount(story.viewCount);
    }
    
  return InkWell(
    onTap: () async { /* ... onTap逻辑保持不变 ... */ },
    borderRadius: BorderRadius.circular(16),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Stack(
        children: [
          // 背景图
          Positioned.fill(
            child: Image.network(
              coverUrl ?? 'https://via.placeholder.com/300x400?text=Error',
              fit: BoxFit.cover,
              errorBuilder: (_,__,___) => Container(color: AppColors.secondaryBg),
            ),
          ),
          // 渐变遮罩
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.transparent, Colors.black.withOpacity(0.9)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0.5, 1.0],
                ),
              ),
            ),
          ),
          // 文字信息
          Positioned(
            bottom: 12,
            left: 12,
            right: 12,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标签
                if (tags.isNotEmpty)
                  Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children: tags.take(2).map((tag) => Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(tag, style: const TextStyle(color: Colors.white, fontSize: 10)),
                    )).toList(),
                  ),
                const SizedBox(height: 6),
                // 标题
                Text(
                  title,
                  style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                // 人气
                Row(
                  children: [
                    const Icon(Icons.favorite, color: AppColors.secondaryText, size: 12),
                    const SizedBox(width: 4),
                    Text(popularity, style: const TextStyle(color: AppColors.secondaryText, fontSize: 12)),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

// 别忘了在_WatchPageState中添加 _formatCount 方法
String _formatCount(int count) {
  if (count >= 10000) {
    return '${(count / 10000).toStringAsFixed(1)}万';
  }
  return count.toString();
}
```

---

### **4.【我的】页面 (`ProfilePage`) 新建**

**目标：** 从零开始，创建一个功能和视觉都对标竞品的个人中心页面。

#### **文件: `lib/src/features/profile/presentation/pages/profile_page.dart`**

**将整个文件的内容替换为以下代码：**

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package/xinglian/src/features/auth/bloc/auth_bloc.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            if (state is Authenticated) {
              return _buildAuthenticatedProfile(context, state);
            }
            // 可以为游客状态或其他状态提供一个后备UI
            return const Center(child: Text("请先登录", style: TextStyle(color: Colors.white)));
          },
        ),
      ),
    );
  }

  Widget _buildAuthenticatedProfile(BuildContext context, Authenticated state) {
    return Column(
      children: [
        _buildUserInfoSection(state),
        _buildStatsSection(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildBondTab(),
              _buildCreationTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfoSection(Authenticated state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              // 发光外环
              Container(
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accentPurple.withOpacity(0.5),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                  ],
                ),
              ),
              const CircleAvatar(
                radius: 40,
                // TODO: 替换为真实用户头像
                backgroundImage: NetworkImage('https://i.imgur.com/example_user_avatar.png'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            '破次元居民 zxdLWNrD', // TODO: 替换为真实昵称
            style: TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            'UID: ${state.userId.substring(0, 8)}', // 显示部分UID
            style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
          ),
          const SizedBox(height: 8),
          const Text(
            '这个人很懒，森米信息都没留下～ ✍️',
            style: TextStyle(color: AppColors.secondaryText, fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('我邂逅过', '7'),
          _buildStatItem('我的羁绊', '4'),
          _buildStatItem('我的自由捏', '0'),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(value, style: const TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold)),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(color: AppColors.secondaryText, fontSize: 14)),
      ],
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      labelColor: AppColors.primaryText,
      unselectedLabelColor: AppColors.secondaryText,
      indicator: const UnderlineTabIndicator(
        borderSide: BorderSide(width: 2.0, color: AppColors.accentPurple),
        insets: EdgeInsets.symmetric(horizontal: 60.0),
      ),
      tabs: const [
        Tab(text: '羁绊'),
        Tab(text: '自由捏'),
      ],
    );
  }

  Widget _buildBondTab() {
    // 模拟数据
    final bondCharacters = [
      {'name': '傅止洲', 'avatar': 'https://i.imgur.com/avatar1.png', 'level': 2},
      {'name': '栢', 'avatar': 'https://i.imgur.com/avatar2.png', 'level': 1},
      {'name': '黎川', 'avatar': 'https://i.imgur.com/avatar3.png', 'level': 1},
      {'name': '年岁安', 'avatar': 'https://i.imgur.com/avatar4.png', 'level': 1},
    ];
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('我的羁绊 (4)', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
              TextButton(onPressed: (){}, child: const Text('查看全部 >', style: TextStyle(color: AppColors.secondaryText, fontSize: 12))),
            ],
          ),
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 0.8,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: bondCharacters.length,
            itemBuilder: (context, index) {
              final char = bondCharacters[index];
              return Column(
                children: [
                  CircleAvatar(radius: 30, backgroundImage: NetworkImage(char['avatar']!)),
                  const SizedBox(height: 8),
                  Text(char['name']!, style: const TextStyle(color: Colors.white, fontSize: 12)),
                  Text('Lv.${char['level']}', style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                ],
              );
            },
          ),
          const SizedBox(height: 24),
          const Text('我的礼物', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          // 空状态
          Center(
            child: Column(
              children: [
                Icon(Icons.card_giftcard, size: 48, color: AppColors.secondaryText),
                const SizedBox(height: 8),
                const Text('你还没有收到礼物哦～', style: TextStyle(color: AppColors.secondaryText)),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildCreationTab() {
    return const Center(
      child: Text('自由捏功能开发中...', style: TextStyle(color: AppColors.secondaryText)),
    );
  }
}
```

---

**总结与后续步骤:**

以上代码修改和新增文件，严格对标了产品经理的优化方案。

1.  **首要任务**是应用**P0级导航BUG修复**，使应用恢复基本可用性。
2.  其次，逐个文件应用【聊天】、【羁绊】和【推荐】页面的UI和逻辑修改。
3.  最后，创建全新的【我的】页面文件`profile_page.dart`并粘贴代码。
4.  **后端协作**：请务必与后端工程师沟通，确保API接口能够提供前端所需的新增数据字段（如等级、事件标签、羁绊值等），否则UI将无法正确渲染。

完成以上步骤后，应用的核心页面将在视觉和体验上达到竞品水平。