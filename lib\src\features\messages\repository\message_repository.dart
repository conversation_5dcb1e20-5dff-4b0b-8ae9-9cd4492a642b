// lib/src/features/messages/repository/message_repository.dart
import '../../../core/networking/api_client.dart';
import '../models/chat_list_item.dart';

class MessageRepository {
  final ApiClient _apiClient;
  MessageRepository(this._apiClient);

  Future<List<ChatListItem>> fetchChatList(String userId) async {
    try {
      final response = await _apiClient.dio.get('/api/user-chats', queryParameters: {'user_id': userId});
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data.map((json) => ChatListItem.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('获取聊天列表失败: $e');
      throw Exception('获取聊天列表失败');
    }
  }
}
