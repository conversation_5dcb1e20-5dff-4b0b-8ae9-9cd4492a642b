import 'dart:ui';
import 'package:flutter/material.dart';

/// 玻璃拟态容器组件
/// 
/// 实现半透明模糊效果，营造现代化的玻璃质感
/// 适用于导航栏、弹窗、卡片等需要玻璃拟态效果的UI元素
class GlassmorphismContainer extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 模糊强度
  final double blur;
  
  /// 背景颜色（会自动应用透明度）
  final Color color;
  
  /// 圆角半径
  final BorderRadius borderRadius;
  
  /// 边框样式
  final Border? border;
  
  /// 背景透明度（0.0-1.0）
  final double opacity;

  const GlassmorphismContainer({
    Key? key,
    required this.child,
    this.blur = 10.0,
    this.color = Colors.white,
    this.borderRadius = BorderRadius.zero,
    this.border,
    this.opacity = 0.15,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius,
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          decoration: BoxDecoration(
            color: color.withOpacity(opacity),
            borderRadius: borderRadius,
            border: border ?? Border.all(
              color: Colors.white.withOpacity(0.2), 
              width: 1.0,
            ),
          ),
          child: child,
        ),
      ),
    );
  }
}

/// 预设的玻璃拟态样式
class GlassmorphismStyles {
  /// 导航栏样式
  static GlassmorphismContainer navigationBar({required Widget child}) {
    return GlassmorphismContainer(
      blur: 15.0,
      color: const Color(0xFF2C2A4A),
      opacity: 0.8,
      borderRadius: BorderRadius.zero,
      border: Border(
        top: BorderSide(
          color: Colors.white.withOpacity(0.1),
          width: 0.5,
        ),
      ),
      child: child,
    );
  }

  /// 卡片样式
  static GlassmorphismContainer card({required Widget child}) {
    return GlassmorphismContainer(
      blur: 8.0,
      color: const Color(0xFF2C2A4A),
      opacity: 0.6,
      borderRadius: BorderRadius.circular(12),
      child: child,
    );
  }

  /// 按钮样式
  static GlassmorphismContainer button({required Widget child}) {
    return GlassmorphismContainer(
      blur: 5.0,
      color: const Color(0xFFC974FF),
      opacity: 0.3,
      borderRadius: BorderRadius.circular(20),
      child: child,
    );
  }
}
