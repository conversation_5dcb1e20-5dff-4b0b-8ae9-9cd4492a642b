import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../auth/bloc/auth_bloc.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: const Text(
          '我的',
          style: TextStyle(
            color: AppColors.primaryText,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings_outlined, color: AppColors.primaryText),
            onPressed: () {
              // TODO: 打开设置页面
            },
          ),
        ],
      ),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          if (state is Authenticated) {
            return _buildAuthenticatedProfile(context, state);
          } else {
            return _buildGuestProfile(context);
          }
        },
      ),
    );
  }

  Widget _buildAuthenticatedProfile(BuildContext context, Authenticated state) {
    return Column(
      children: [
        _buildUserInfoSection(state),
        _buildStatsSection(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildBondTab(),
              _buildCreationTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfoSection(Authenticated state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              // 发光外环
              Container(
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accentPurple.withOpacity(0.5),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                  ],
                ),
              ),
              const CircleAvatar(
                radius: 40,
                // TODO: 替换为真实用户头像
                backgroundImage: NetworkImage('https://i.imgur.com/example_user_avatar.png'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            '破次元居民 zxdLWNrD', // TODO: 替换为真实昵称
            style: TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            'UID: ${state.userId.substring(0, 8)}', // 显示部分UID
            style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
          ),
          const SizedBox(height: 8),
          const Text(
            '这个人很懒，森米信息都没留下～ ✍️',
            style: TextStyle(color: AppColors.secondaryText, fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('我邂逅过', '7'),
          _buildStatItem('我的羁绊', '4'),
          _buildStatItem('我的自由捏', '0'),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(value, style: const TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold)),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(color: AppColors.secondaryText, fontSize: 14)),
      ],
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      labelColor: AppColors.primaryText,
      unselectedLabelColor: AppColors.secondaryText,
      indicator: const UnderlineTabIndicator(
        borderSide: BorderSide(width: 2.0, color: AppColors.accentPurple),
        insets: EdgeInsets.symmetric(horizontal: 60.0),
      ),
      tabs: const [
        Tab(text: '羁绊'),
        Tab(text: '自由捏'),
      ],
    );
  }

  Widget _buildBondTab() {
    // 模拟数据
    final bondCharacters = [
      {'name': '傅止洲', 'avatar': 'https://i.imgur.com/avatar1.png', 'level': 2},
      {'name': '栢', 'avatar': 'https://i.imgur.com/avatar2.png', 'level': 1},
      {'name': '黎川', 'avatar': 'https://i.imgur.com/avatar3.png', 'level': 1},
      {'name': '年岁安', 'avatar': 'https://i.imgur.com/avatar4.png', 'level': 1},
    ];
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('我的羁绊 (4)', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
              TextButton(onPressed: (){}, child: const Text('查看全部 >', style: TextStyle(color: AppColors.secondaryText, fontSize: 12))),
            ],
          ),
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 0.8,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: bondCharacters.length,
            itemBuilder: (context, index) {
              final char = bondCharacters[index];
              return Column(
                children: [
                  CircleAvatar(radius: 30, backgroundImage: NetworkImage(char['avatar']!)),
                  const SizedBox(height: 8),
                  Text(char['name']!, style: const TextStyle(color: Colors.white, fontSize: 12)),
                  Text('Lv.${char['level']}', style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                ],
              );
            },
          ),
          const SizedBox(height: 24),
          const Text('我的礼物', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          // 空状态
          Center(
            child: Column(
              children: [
                Icon(Icons.card_giftcard, size: 48, color: AppColors.secondaryText),
                const SizedBox(height: 8),
                const Text('你还没有收到礼物哦～', style: TextStyle(color: AppColors.secondaryText)),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildCreationTab() {
    return const Center(
      child: Text('自由捏功能开发中...', style: TextStyle(color: AppColors.secondaryText)),
    );
  }






  Widget _buildGuestProfile(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.inputBackground.withValues(alpha: 0.3),
              ),
              child: const Icon(
                Icons.person_outline,
                color: AppColors.secondaryText,
                size: 50,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              '登录后享受更多功能',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '个性化推荐、专属记忆、创作分享',
              style: TextStyle(
                color: AppColors.secondaryText,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  context.push('/login');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.accentYellow,
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  '立即登录',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

}
